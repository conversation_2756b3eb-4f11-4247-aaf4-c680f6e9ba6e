@echo off
REM ================================================================================
REM File Transfer Standalone Server Start Script (Windows Version)
REM ================================================================================

setlocal enabledelayedexpansion

REM ==================== Constants ====================

set "SCRIPT_VERSION=1.0.0"
set "SCRIPT_NAME=File Transfer Standalone Server Start Script"

REM Default Java 8 JDK path
set "DEFAULT_JAVA8_HOME=%USERPROFILE%\.jdks\corretto-1.8.0_452"

REM Service configuration
set "DEFAULT_SERVER_PORT=49011"
set "DEFAULT_PROFILE=server"
set "DEFAULT_CONFIG_FILE=application.yml"

REM JAR file configuration
set "JAR_NAME=file-transfer-server-standalone-1.0.0.jar"
set "TARGET_DIR=target"

REM Log configuration
set "LOG_DIR=logs"
set "PID_FILE=%LOG_DIR%\server.pid"
set "LOG_FILE=%LOG_DIR%\server.log"

REM Timeout configuration (seconds)
set "STARTUP_TIMEOUT=60"
set "SHUTDOWN_TIMEOUT=30"

REM ==================== Utility Functions ====================

:log_info
echo [INFO] %date% %time% - %~1
goto :eof

:log_success
echo [SUCCESS] %date% %time% - %~1
goto :eof

:log_warning
echo [WARNING] %date% %time% - %~1
goto :eof

:log_error
echo [ERROR] %date% %time% - %~1
goto :eof

:show_header
echo ========================================================
echo     %SCRIPT_NAME%
echo     Version: %SCRIPT_VERSION%
echo     Time: %date% %time%
echo ========================================================
goto :eof

:show_help
echo Usage: %~nx0 [command] [options]
echo.
echo Commands:
echo   start     Start server
echo   stop      Stop server
echo   restart   Restart server
echo   status    View server status
echo   logs      View server logs
echo.
echo Options:
echo   --port PORT           Specify server port (default: %DEFAULT_SERVER_PORT%)
echo   --profile PROFILE     Specify Spring profile (default: %DEFAULT_PROFILE%)
echo   --config CONFIG       Specify config file path (default: %DEFAULT_CONFIG_FILE%)
echo   --java-home PATH      Specify Java JDK path
echo   --background          Run server in background
echo   --help                Show this help information
echo.
echo Examples:
echo   %~nx0 start                    # Start server
echo   %~nx0 start --port 8080        # Start server on port 8080
echo   %~nx0 start --background       # Start server in background
echo   %~nx0 stop                     # Stop server
echo   %~nx0 status                   # View server status
echo.
goto :eof

:init_logging
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
    call :log_info "Created log directory: %LOG_DIR%"
)
goto :eof

:setup_java_environment
set "custom_java_home=%~1"

REM If custom Java path is specified, use it
if not "%custom_java_home%"=="" (
    if exist "%custom_java_home%\bin\java.exe" (
        set "JAVA_HOME=%custom_java_home%"
        set "PATH=%custom_java_home%\bin;%PATH%"
        call :log_info "Using specified Java JDK: %custom_java_home%"
        goto :setup_java_done
    ) else (
        call :log_error "Specified Java JDK path is invalid: %custom_java_home%"
        exit /b 1
    )
)

REM Check if default Java 8 JDK exists
if exist "%DEFAULT_JAVA8_HOME%\bin\java.exe" (
    set "JAVA_HOME=%DEFAULT_JAVA8_HOME%"
    set "PATH=%DEFAULT_JAVA8_HOME%\bin;%PATH%"
    call :log_info "Using default Java 8 JDK: %DEFAULT_JAVA8_HOME%"
) else (
    call :log_warning "Default Java 8 JDK not found, using system default Java"
)

:setup_java_done
REM Verify Java command availability
java -version >nul 2>&1
if errorlevel 1 (
    call :log_error "Java runtime not installed or not in PATH"
    exit /b 1
)

REM Get Java version information
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set "java_version=%%i"
    goto :java_version_done
)
:java_version_done
call :log_info "Current Java version: %java_version%"
goto :eof

:check_jar_file
set "jar_path=%TARGET_DIR%\%JAR_NAME%"

if not exist "%jar_path%" (
    call :log_error "JAR file does not exist: %jar_path%"
    call :log_info "Please run 'mvn clean package' to compile the project first"
    exit /b 1
)

call :log_info "Found JAR file: %jar_path%"
goto :eof

:check_port
set "port=%~1"

REM Use netstat to check if port is occupied
netstat -an | findstr ":%port% " >nul 2>&1
if not errorlevel 1 (
    call :log_warning "Port %port% is already in use"
    exit /b 1
)
goto :eof

:get_server_pid
if exist "%PID_FILE%" (
    set /p server_pid=<"%PID_FILE%"
    REM Check if process is still running
    tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
    if not errorlevel 1 (
        exit /b 0
    ) else (
        REM PID file exists but process doesn't exist, delete PID file
        del "%PID_FILE%" >nul 2>&1
    )
)
exit /b 1

:start_server
set "port=%~1"
set "profile=%~2"
set "config_file=%~3"
set "background=%~4"

call :log_info "Starting file transfer server..."

REM Check if server is already running
call :get_server_pid
if not errorlevel 1 (
    call :log_warning "Server is already running (PID: %server_pid%)"
    exit /b 1
)

REM Check port
call :check_port "%port%"
if errorlevel 1 (
    call :log_error "Cannot start server, port %port% is already in use"
    exit /b 1
)

REM Check JAR file
call :check_jar_file
if errorlevel 1 exit /b 1

REM Build Java command
set "jar_path=%TARGET_DIR%\%JAR_NAME%"
set "java_opts=-Xms512m -Xmx1g -XX:+UseG1GC"
set "spring_opts=--server.port=%port% --spring.profiles.active=%profile%"

if not "%config_file%"=="%DEFAULT_CONFIG_FILE%" (
    set "spring_opts=%spring_opts% --spring.config.location=classpath:/%config_file%"
)

set "java_cmd=java %java_opts% -jar %jar_path% %spring_opts%"
call :log_info "Java command: %java_cmd%"

if "%background%"=="true" (
    REM Background startup
    start /b "" %java_cmd% > "%LOG_FILE%" 2>&1
    
    REM Get process ID (Windows way)
    timeout /t 2 /nobreak >nul
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "%JAR_NAME%"') do (
        set "pid=%%i"
        echo %pid% > "%PID_FILE%"
        goto :pid_found
    )
    
    :pid_found
    call :log_info "Server starting in background (PID: %pid%)..."
    
    REM Wait for server startup
    set /a wait_count=0
    :wait_startup
    if %wait_count% geq %STARTUP_TIMEOUT% goto :startup_timeout
    
    REM Use curl to check health status
    curl -s "http://localhost:%port%/filetransfer/actuator/health" >nul 2>&1
    if not errorlevel 1 (
        call :log_success "Server started successfully!"
        call :log_info "Server address: http://localhost:%port%"
        call :log_info "API documentation: http://localhost:%port%/filetransfer/doc.html"
        call :log_info "Health check: http://localhost:%port%/filetransfer/actuator/health"
        call :log_info "Log file: %LOG_FILE%"
        call :log_info "PID file: %PID_FILE%"
        exit /b 0
    )
    
    timeout /t 1 /nobreak >nul
    set /a wait_count+=1
    
    REM Check if process is still running
    tasklist /fi "pid eq %pid%" 2>nul | findstr "%pid%" >nul
    if errorlevel 1 (
        call :log_error "Server process exited unexpectedly"
        del "%PID_FILE%" >nul 2>&1
        exit /b 1
    )
    goto :wait_startup
    
    :startup_timeout
    call :log_error "Server startup timeout"
    call :stop_server
    exit /b 1
) else (
    REM Foreground startup
    call :log_info "Server starting in foreground..."
    call :log_info "Press Ctrl+C to stop server"
    %java_cmd%
)
goto :eof

:stop_server
call :log_info "Stopping file transfer server..."

call :get_server_pid
if errorlevel 1 (
    call :log_warning "Server is not running"
    exit /b 0
)

call :log_info "Stopping server process (PID: %server_pid%)..."

REM Try graceful shutdown
taskkill /pid %server_pid% >nul 2>&1

REM Wait for process to exit
set /a wait_count=0
:wait_shutdown
if %wait_count% geq %SHUTDOWN_TIMEOUT% goto :force_kill

tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
if errorlevel 1 (
    del "%PID_FILE%" >nul 2>&1
    call :log_success "Server stopped"
    exit /b 0
)

timeout /t 1 /nobreak >nul
set /a wait_count+=1
goto :wait_shutdown

:force_kill
REM Force kill process
call :log_warning "Force stopping server process..."
taskkill /f /pid %server_pid% >nul 2>&1
del "%PID_FILE%" >nul 2>&1
call :log_success "Server force stopped"
exit /b 0

:show_status
call :get_server_pid
if not errorlevel 1 (
    call :log_success "Server is running (PID: %server_pid%)"
    
    REM Try to get server information
    set "port=%DEFAULT_SERVER_PORT%"
    
    echo Server information:
    echo   PID: %server_pid%
    echo   Port: %port%
    echo   Server address: http://localhost:%port%
    echo   API documentation: http://localhost:%port%/filetransfer/doc.html
    echo   Health check: http://localhost:%port%/filetransfer/actuator/health
    echo   Log file: %LOG_FILE%
    echo   PID file: %PID_FILE%

    REM Check health status
    curl -s "http://localhost:%port%/filetransfer/actuator/health" >nul 2>&1
    if not errorlevel 1 (
        call :log_success "Server health check passed"
    ) else (
        call :log_warning "Server health check failed"
    )
) else (
    call :log_info "Server is not running"
)
goto :eof

:show_logs
if exist "%LOG_FILE%" (
    call :log_info "Showing server logs: %LOG_FILE%"
    echo ========================================
    type "%LOG_FILE%"
) else (
    call :log_warning "Log file does not exist: %LOG_FILE%"
)
goto :eof

:restart_server
set "port=%~1"
set "profile=%~2"
set "config_file=%~3"
set "background=%~4"

call :log_info "Restarting file transfer server..."

REM Stop server
call :stop_server

REM Wait one second
timeout /t 1 /nobreak >nul

REM Start server
call :start_server "%port%" "%profile%" "%config_file%" "%background%"
goto :eof

REM ==================== Main Program ====================

:main
REM Show script header
call :show_header

REM Initialize logging
call :init_logging

REM Parse command line arguments
set "command="
set "port=%DEFAULT_SERVER_PORT%"
set "profile=%DEFAULT_PROFILE%"
set "config_file=%DEFAULT_CONFIG_FILE%"
set "java_home="
set "background=false"

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="start" (
    set "command=start"
    shift
    goto :parse_args
)
if "%~1"=="stop" (
    set "command=stop"
    shift
    goto :parse_args
)
if "%~1"=="restart" (
    set "command=restart"
    shift
    goto :parse_args
)
if "%~1"=="status" (
    set "command=status"
    shift
    goto :parse_args
)
if "%~1"=="logs" (
    set "command=logs"
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set "port=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--profile" (
    set "profile=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--config" (
    set "config_file=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--java-home" (
    set "java_home=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--background" (
    set "background=true"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
call :log_error "Unknown option: %~1"
echo.
call :show_help
exit /b 1

:args_done
REM If no command specified, show help
if "%command%"=="" (
    call :show_help
    exit /b 1
)

REM Setup Java environment
call :setup_java_environment "%java_home%"
if errorlevel 1 exit /b 1

REM Execute command
if "%command%"=="start" (
    call :start_server "%port%" "%profile%" "%config_file%" "%background%"
) else if "%command%"=="stop" (
    call :stop_server
) else if "%command%"=="restart" (
    call :restart_server "%port%" "%profile%" "%config_file%" "%background%"
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="logs" (
    call :show_logs
) else (
    call :log_error "Unknown command: %command%"
    exit /b 1
)

goto :eof

REM Execute main function
call :main %*
