@echo off
REM Simple test script to debug start-server-en.bat issues

echo Testing start-server-en.bat script...
echo.

REM Test 1: Check if script can parse basic arguments
echo [TEST 1] Testing basic argument parsing...
echo Command: start-server-en.bat --help
echo.

REM Test 2: Check Java environment
echo [TEST 2] Testing Java environment...
java -version
echo.

REM Test 3: Check JAR file
echo [TEST 3] Testing JAR file existence...
if exist "target\file-transfer-server-standalone-1.0.0.jar" (
    echo JAR file exists: target\file-transfer-server-standalone-1.0.0.jar
) else (
    echo JAR file NOT found: target\file-transfer-server-standalone-1.0.0.jar
)
echo.

REM Test 4: Check port availability
echo [TEST 4] Testing port 49011 availability...
netstat -an | findstr ":49011" >nul 2>&1
if errorlevel 1 (
    echo Port 49011 is available
) else (
    echo Port 49011 is already in use
)
echo.

echo Test completed.
pause
