@echo off
REM ================================================================================
REM Simple File Transfer SDK Build Script (No Functions)
REM ================================================================================

setlocal enabledelayedexpansion

echo ========================================================
echo     Simple File Transfer SDK Build Script
echo     Version: 1.0.0
echo     Time: %date% %time%
echo ========================================================

REM Parse command line arguments
set "execution_mode=build-test"
if "%~1"=="build" set "execution_mode=build"
if "%~1"=="build-test" set "execution_mode=build-test"
if "%~1"=="--help" (
    echo Usage: %~nx0 [build^|build-test^|--help]
    echo   build       - Build only
    echo   build-test  - Build and test [default]
    echo   --help      - Show this help
    exit /b 0
)

echo [INFO] %date% %time% - Execution mode: %execution_mode%

REM Step 1: Setup Java environment
echo [STEP 1] %date% %time% - Setting up Java environment
echo ========================================

if exist "scripts\set-java-env-en.bat" (
    echo [INFO] %date% %time% - Calling Java environment setup script
    call "scripts\set-java-env-en.bat"
    if errorlevel 1 (
        echo [ERROR] %date% %time% - Java environment setup script failed
        exit /b 1
    )
    echo [INFO] %date% %time% - Java environment setup script executed successfully
) else (
    echo [WARNING] %date% %time% - Java environment setup script not found, using system default Java
)

where java >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %date% %time% - Java Runtime not installed or not in PATH
    exit /b 1
)

echo [SUCCESS] %date% %time% - Java environment setup completed

REM Step 2: Check Maven environment
echo [STEP 2] %date% %time% - Checking Maven environment
echo ========================================

where mvn >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %date% %time% - Apache Maven not installed or not in PATH
    exit /b 1
)

echo [INFO] %date% %time% - Maven found in PATH
echo [SUCCESS] %date% %time% - Maven environment check completed

REM Step 3: Validate project structure
echo [STEP 3] %date% %time% - Validating project structure
echo ========================================

if not exist "pom.xml" (
    echo [ERROR] %date% %time% - Root pom.xml file does not exist
    exit /b 1
)
echo [INFO] %date% %time% - Root pom.xml file exists
echo [SUCCESS] %date% %time% - Project structure validation completed

REM Step 4: Clean environment
echo [STEP 4] %date% %time% - Cleaning build environment
echo ========================================

echo [INFO] %date% %time% - Cleaning Maven build cache...
echo [DEBUG] About to execute: call mvn clean
call mvn clean
echo [DEBUG] Maven clean completed with errorlevel: %errorlevel%
if errorlevel 1 (
    echo [WARNING] %date% %time% - Maven clean failed, continuing anyway
)
echo [SUCCESS] %date% %time% - Environment cleanup completed
echo [DEBUG] About to proceed to Step 5

REM Step 5: Compile project
echo [STEP 5] %date% %time% - Compiling project
echo ========================================

echo [INFO] %date% %time% - Starting compilation of entire project...
echo [INFO] %date% %time% - Compile command: mvn compile -T 1C

call mvn compile -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
if errorlevel 1 (
    echo [ERROR] %date% %time% - Project compilation failed
    exit /b 1
)
echo [SUCCESS] %date% %time% - Project compilation successful

REM Step 6: Install project
echo [STEP 6] %date% %time% - Installing project to local Maven repository
echo ========================================

echo [INFO] %date% %time% - Starting installation of project to local Maven repository...
echo [INFO] %date% %time% - Install command: mvn install -DskipTests -T 1C

call mvn install -DskipTests -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
if errorlevel 1 (
    echo [ERROR] %date% %time% - Project installation failed
    exit /b 1
)
echo [SUCCESS] %date% %time% - Project installation successful

REM Steps 7-8: Test and package (only in build-test mode)
if "%execution_mode%"=="build-test" (
    REM Step 7: Run unit tests
    echo [STEP 7] %date% %time% - Running unit tests
    echo ========================================
    
    echo [INFO] %date% %time% - Starting unit tests...
    echo [INFO] %date% %time% - Test command: mvn test -T 1C
    
    call mvn test -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
    if errorlevel 1 (
        echo [ERROR] %date% %time% - Unit tests failed
        exit /b 1
    )
    echo [SUCCESS] %date% %time% - Unit tests completed successfully
    
    REM Step 8: Package project
    echo [STEP 8] %date% %time% - Packaging project
    echo ========================================
    
    echo [INFO] %date% %time% - Starting project packaging...
    echo [INFO] %date% %time% - Package command: mvn package -DskipTests -T 1C
    
    call mvn package -DskipTests -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
    if errorlevel 1 (
        echo [ERROR] %date% %time% - Project packaging failed
        exit /b 1
    )
    echo [SUCCESS] %date% %time% - Project packaging successful
)

REM Final result
if "%execution_mode%"=="build" (
    echo [SUCCESS] %date% %time% - Build completed successfully
) else (
    echo [SUCCESS] %date% %time% - Build and test completed successfully
)

echo ========================================================
echo     Build script completed successfully!
echo     Time: %date% %time%
echo ========================================================

exit /b 0
