@echo off
REM ================================================================================
REM Simple File Transfer Server Start Script
REM ================================================================================

echo ========================================================
echo     Simple File Transfer Server Start Script
echo     Version: 1.0.0-SIMPLE
echo     Time: %date% %time%
echo ========================================================

REM Configuration
set "JAR_FILE=target\file-transfer-server-standalone-1.0.0.jar"
set "SERVER_PORT=49011"
set "JAVA_OPTS=-Xms512m -Xmx1g -XX:+UseG1GC"
set "SPRING_OPTS=--server.port=%SERVER_PORT% --spring.profiles.active=server"

REM Check command line arguments
if "%1"=="--help" goto :show_help
if "%1"=="help" goto :show_help
if "%1"=="start" goto :start_server
if "%1"=="stop" goto :stop_server
if "%1"=="status" goto :show_status
if "%1"=="" goto :start_server

echo [ERROR] Unknown command: %1
goto :show_help

:show_help
echo Usage: %~nx0 [command]
echo.
echo Commands:
echo   start     Start server (default)
echo   stop      Stop server
echo   status    Show server status
echo   help      Show this help
echo.
echo Examples:
echo   %~nx0                # Start server
echo   %~nx0 start          # Start server
echo   %~nx0 stop           # Stop server
echo   %~nx0 status         # Show status
echo.
exit /b 0

:start_server
echo [INFO] Starting file transfer server...

REM Check Java
java -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java not found in PATH
    exit /b 1
)

REM Check JAR file
if not exist "%JAR_FILE%" (
    echo [ERROR] JAR file not found: %JAR_FILE%
    echo Please run 'mvn clean package' first
    exit /b 1
)

REM Check port
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Port %SERVER_PORT% is already in use
    echo Server may already be running
)

REM Start server
echo [INFO] Starting server on port %SERVER_PORT%...
echo [INFO] Java command: java %JAVA_OPTS% -jar %JAR_FILE% %SPRING_OPTS%
echo [INFO] Press Ctrl+C to stop server
echo.

java %JAVA_OPTS% -jar %JAR_FILE% %SPRING_OPTS%
exit /b %errorlevel%

:stop_server
echo [INFO] Stopping file transfer server...

REM Find Java processes running our JAR
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "file-transfer-server"') do (
    echo [INFO] Stopping process %%i...
    taskkill /f /pid %%i >nul 2>&1
)

REM Alternative: Kill all Java processes (more aggressive)
echo [INFO] Stopping all Java processes...
taskkill /f /im java.exe >nul 2>&1

echo [SUCCESS] Server stop command completed
exit /b 0

:show_status
echo [INFO] Checking server status...

REM Check if port is listening
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] Server is running on port %SERVER_PORT%
    echo Server address: http://localhost:%SERVER_PORT%
    echo Health check: http://localhost:%SERVER_PORT%/filetransfer/actuator/health
) else (
    echo [INFO] Server is not running on port %SERVER_PORT%
)

REM Check Java processes
echo.
echo Java processes:
tasklist /fi "imagename eq java.exe" 2>nul | findstr "java.exe"
if errorlevel 1 (
    echo No Java processes found
)

exit /b 0
