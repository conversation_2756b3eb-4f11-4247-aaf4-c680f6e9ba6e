@echo off
REM ================================================================================
REM 文件传输独立服务端启动脚本 (Windows版本)
REM ================================================================================

setlocal enabledelayedexpansion

REM ==================== 常量定义 ====================

REM 脚本版本信息
set "SCRIPT_VERSION=1.0.0"
set "SCRIPT_NAME=文件传输独立服务端启动脚本"

REM 默认Java 8 JDK路径
set "DEFAULT_JAVA8_HOME=%USERPROFILE%\.jdks\corretto-1.8.0_452"

REM 服务配置
set "DEFAULT_SERVER_PORT=49011"
set "DEFAULT_PROFILE=server"
set "DEFAULT_CONFIG_FILE=application.yml"

REM JAR文件配置
set "JAR_NAME=file-transfer-server-standalone-1.0.0.jar"
set "TARGET_DIR=target"

REM 日志配置
set "LOG_DIR=logs"
set "PID_FILE=%LOG_DIR%\server.pid"
set "LOG_FILE=%LOG_DIR%\server.log"

REM 超时配置（秒）
set "STARTUP_TIMEOUT=60"
set "SHUTDOWN_TIMEOUT=30"

REM ==================== 工具函数 ====================

:log_info
echo [INFO] %date% %time% - %~1
goto :eof

:log_success
echo [SUCCESS] %date% %time% - %~1
goto :eof

:log_warning
echo [WARNING] %date% %time% - %~1
goto :eof

:log_error
echo [ERROR] %date% %time% - %~1
goto :eof

:show_header
echo ========================================================
echo     %SCRIPT_NAME%
echo     版本：%SCRIPT_VERSION%
echo     时间：%date% %time%
echo ========================================================
goto :eof

:show_help
echo 用法: %~nx0 [命令] [选项]
echo.
echo 命令:
echo   start     启动服务器
echo   stop      停止服务器
echo   restart   重启服务器
echo   status    查看服务器状态
echo   logs      查看服务器日志
echo.
echo 选项:
echo   --port PORT           指定服务器端口 (默认: %DEFAULT_SERVER_PORT%)
echo   --profile PROFILE     指定Spring配置文件 (默认: %DEFAULT_PROFILE%)
echo   --config CONFIG       指定配置文件路径 (默认: %DEFAULT_CONFIG_FILE%)
echo   --java-home PATH      指定Java JDK路径
echo   --background          后台运行服务器
echo   --help                显示此帮助信息
echo.
echo 示例:
echo   %~nx0 start                    # 启动服务器
echo   %~nx0 start --port 8080        # 在端口8080启动服务器
echo   %~nx0 start --background       # 后台启动服务器
echo   %~nx0 stop                     # 停止服务器
echo   %~nx0 status                   # 查看服务器状态
echo.
goto :eof

:init_logging
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
    call :log_info "创建日志目录：%LOG_DIR%"
)
goto :eof

:setup_java_environment
set "custom_java_home=%~1"

REM 如果指定了自定义Java路径，使用它
if not "%custom_java_home%"=="" (
    if exist "%custom_java_home%\bin\java.exe" (
        set "JAVA_HOME=%custom_java_home%"
        set "PATH=%custom_java_home%\bin;%PATH%"
        call :log_info "使用指定的Java JDK：%custom_java_home%"
        goto :setup_java_done
    ) else (
        call :log_error "指定的Java JDK路径无效：%custom_java_home%"
        exit /b 1
    )
)

REM 检查默认的Java 8 JDK是否存在
if exist "%DEFAULT_JAVA8_HOME%\bin\java.exe" (
    set "JAVA_HOME=%DEFAULT_JAVA8_HOME%"
    set "PATH=%DEFAULT_JAVA8_HOME%\bin;%PATH%"
    call :log_info "使用默认的Java 8 JDK：%DEFAULT_JAVA8_HOME%"
) else (
    call :log_warning "未找到默认Java 8 JDK，使用系统默认Java"
)

:setup_java_done
REM 验证Java命令可用性
java -version >nul 2>&1
if errorlevel 1 (
    call :log_error "Java运行时未安装或未在PATH中"
    exit /b 1
)

REM 获取Java版本信息
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set "java_version=%%i"
    goto :java_version_done
)
:java_version_done
call :log_info "当前Java版本：%java_version%"
goto :eof

:check_jar_file
set "jar_path=%TARGET_DIR%\%JAR_NAME%"

if not exist "%jar_path%" (
    call :log_error "JAR文件不存在：%jar_path%"
    call :log_info "请先运行 'mvn clean package' 编译项目"
    exit /b 1
)

call :log_info "找到JAR文件：%jar_path%"
goto :eof

:check_port
set "port=%~1"

REM 使用netstat检查端口是否被占用
netstat -an | findstr ":%port% " >nul 2>&1
if not errorlevel 1 (
    call :log_warning "端口 %port% 已被占用"
    exit /b 1
)
goto :eof

:get_server_pid
if exist "%PID_FILE%" (
    set /p server_pid=<"%PID_FILE%"
    REM 检查进程是否还在运行
    tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
    if not errorlevel 1 (
        exit /b 0
    ) else (
        REM PID文件存在但进程不存在，删除PID文件
        del "%PID_FILE%" >nul 2>&1
    )
)
exit /b 1

REM ==================== 主要功能函数 ====================

:start_server
set "port=%~1"
set "profile=%~2"
set "config_file=%~3"
set "background=%~4"

call :log_info "启动文件传输服务器..."

REM 检查服务器是否已经运行
call :get_server_pid
if not errorlevel 1 (
    call :log_warning "服务器已经在运行 (PID: %server_pid%)"
    exit /b 1
)

REM 检查端口
call :check_port "%port%"
if errorlevel 1 (
    call :log_error "无法启动服务器，端口 %port% 已被占用"
    exit /b 1
)

REM 检查JAR文件
call :check_jar_file
if errorlevel 1 exit /b 1

REM 构建Java命令
set "jar_path=%TARGET_DIR%\%JAR_NAME%"
set "java_opts=-Xms512m -Xmx1g -XX:+UseG1GC"
set "spring_opts=--server.port=%port% --spring.profiles.active=%profile%"

if not "%config_file%"=="%DEFAULT_CONFIG_FILE%" (
    set "spring_opts=%spring_opts% --spring.config.location=classpath:/%config_file%"
)

set "java_cmd=java %java_opts% -jar %jar_path% %spring_opts%"
call :log_info "Java命令：%java_cmd%"

if "%background%"=="true" (
    REM 后台启动
    start /b "" %java_cmd% > "%LOG_FILE%" 2>&1

    REM 获取进程ID（Windows方式）
    timeout /t 2 /nobreak >nul
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "%JAR_NAME%"') do (
        set "pid=%%i"
        echo %pid% > "%PID_FILE%"
        goto :pid_found
    )

    :pid_found
    call :log_info "服务器后台启动中 (PID: %pid%)..."

    REM 等待服务器启动
    set /a wait_count=0
    :wait_startup
    if %wait_count% geq %STARTUP_TIMEOUT% goto :startup_timeout

    REM 使用curl检查健康状态
    curl -s "http://localhost:%port%/filetransfer/actuator/health" >nul 2>&1
    if not errorlevel 1 (
        call :log_success "服务器启动成功！"
        call :log_info "服务器地址: http://localhost:%port%"
        call :log_info "API文档: http://localhost:%port%/filetransfer/doc.html"
        call :log_info "健康检查: http://localhost:%port%/filetransfer/actuator/health"
        call :log_info "日志文件: %LOG_FILE%"
        call :log_info "PID文件: %PID_FILE%"
        exit /b 0
    )

    timeout /t 1 /nobreak >nul
    set /a wait_count+=1

    REM 检查进程是否还在运行
    tasklist /fi "pid eq %pid%" 2>nul | findstr "%pid%" >nul
    if errorlevel 1 (
        call :log_error "服务器进程意外退出"
        del "%PID_FILE%" >nul 2>&1
        exit /b 1
    )
    goto :wait_startup

    :startup_timeout
    call :log_error "服务器启动超时"
    call :stop_server
    exit /b 1
) else (
    REM 前台启动
    call :log_info "服务器前台启动中..."
    call :log_info "按 Ctrl+C 停止服务器"
    %java_cmd%
)
goto :eof

:stop_server
call :log_info "停止文件传输服务器..."

call :get_server_pid
if errorlevel 1 (
    call :log_warning "服务器未运行"
    exit /b 0
)

call :log_info "停止服务器进程 (PID: %server_pid%)..."

REM 尝试优雅关闭
taskkill /pid %server_pid% >nul 2>&1

REM 等待进程退出
set /a wait_count=0
:wait_shutdown
if %wait_count% geq %SHUTDOWN_TIMEOUT% goto :force_kill

tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
if errorlevel 1 (
    del "%PID_FILE%" >nul 2>&1
    call :log_success "服务器已停止"
    exit /b 0
)

timeout /t 1 /nobreak >nul
set /a wait_count+=1
goto :wait_shutdown

:force_kill
REM 强制杀死进程
call :log_warning "强制停止服务器进程..."
taskkill /f /pid %server_pid% >nul 2>&1
del "%PID_FILE%" >nul 2>&1
call :log_success "服务器已强制停止"
exit /b 0

:show_status
call :get_server_pid
if not errorlevel 1 (
    call :log_success "服务器正在运行 (PID: %server_pid%)"

    REM 尝试获取服务器信息
    set "port=%DEFAULT_SERVER_PORT%"

    echo 服务器信息:
    echo   PID: %server_pid%
    echo   端口: %port%
    echo   服务器地址: http://localhost:%port%
    echo   API文档: http://localhost:%port%/filetransfer/doc.html
    echo   健康检查: http://localhost:%port%/filetransfer/actuator/health
    echo   日志文件: %LOG_FILE%
    echo   PID文件: %PID_FILE%

    REM 检查健康状态
    curl -s "http://localhost:%port%/filetransfer/actuator/health" >nul 2>&1
    if not errorlevel 1 (
        call :log_success "服务器健康检查通过"
    ) else (
        call :log_warning "服务器健康检查失败"
    )
) else (
    call :log_info "服务器未运行"
)
goto :eof

:show_logs
if exist "%LOG_FILE%" (
    call :log_info "显示服务器日志：%LOG_FILE%"
    echo ========================================
    type "%LOG_FILE%"
) else (
    call :log_warning "日志文件不存在：%LOG_FILE%"
)
goto :eof

:restart_server
set "port=%~1"
set "profile=%~2"
set "config_file=%~3"
set "background=%~4"

call :log_info "重启文件传输服务器..."

REM 停止服务器
call :stop_server

REM 等待一秒
timeout /t 1 /nobreak >nul

REM 启动服务器
call :start_server "%port%" "%profile%" "%config_file%" "%background%"
goto :eof

REM ==================== 主程序 ====================

:main
REM 显示脚本头部信息
call :show_header

REM 初始化日志
call :init_logging

REM 解析命令行参数
set "command="
set "port=%DEFAULT_SERVER_PORT%"
set "profile=%DEFAULT_PROFILE%"
set "config_file=%DEFAULT_CONFIG_FILE%"
set "java_home="
set "background=false"

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="start" (
    set "command=start"
    shift
    goto :parse_args
)
if "%~1"=="stop" (
    set "command=stop"
    shift
    goto :parse_args
)
if "%~1"=="restart" (
    set "command=restart"
    shift
    goto :parse_args
)
if "%~1"=="status" (
    set "command=status"
    shift
    goto :parse_args
)
if "%~1"=="logs" (
    set "command=logs"
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set "port=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--profile" (
    set "profile=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--config" (
    set "config_file=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--java-home" (
    set "java_home=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--background" (
    set "background=true"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
call :log_error "未知选项: %~1"
echo.
call :show_help
exit /b 1

:args_done
REM 如果没有指定命令，显示帮助
if "%command%"=="" (
    call :show_help
    exit /b 1
)

REM 设置Java环境
call :setup_java_environment "%java_home%"
if errorlevel 1 exit /b 1

REM 执行命令
if "%command%"=="start" (
    call :start_server "%port%" "%profile%" "%config_file%" "%background%"
) else if "%command%"=="stop" (
    call :stop_server
) else if "%command%"=="restart" (
    call :restart_server "%port%" "%profile%" "%config_file%" "%background%"
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="logs" (
    call :show_logs
) else (
    call :log_error "未知命令: %command%"
    exit /b 1
)

goto :eof

REM 执行主函数
call :main %*
