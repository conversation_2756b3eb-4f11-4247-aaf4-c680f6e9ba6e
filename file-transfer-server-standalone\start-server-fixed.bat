@echo off
REM ================================================================================
REM File Transfer Standalone Server Start Script (Fixed Version)
REM ================================================================================

setlocal enabledelayedexpansion

REM ==================== Constants ====================
set "SCRIPT_VERSION=1.0.1-FIXED"
set "SCRIPT_NAME=File Transfer Standalone Server Start Script (Fixed)"

REM Service configuration
set "DEFAULT_SERVER_PORT=49011"
set "DEFAULT_PROFILE=server"

REM JAR file configuration
set "JAR_NAME=file-transfer-server-standalone-1.0.0.jar"
set "TARGET_DIR=target"

REM Log configuration
set "LOG_DIR=logs"
set "PID_FILE=%LOG_DIR%\server.pid"
set "LOG_FILE=%LOG_DIR%\server.log"

REM ==================== Utility Functions ====================

:log_info
echo [INFO] %date% %time% - %~1
goto :eof

:log_success
echo [SUCCESS] %date% %time% - %~1
goto :eof

:log_warning
echo [WARNING] %date% %time% - %~1
goto :eof

:log_error
echo [ERROR] %date% %time% - %~1
goto :eof

:show_header
echo ========================================================
echo     %SCRIPT_NAME%
echo     Version: %SCRIPT_VERSION%
echo     Time: %date% %time%
echo ========================================================
goto :eof

:show_help
echo Usage: %~nx0 [command] [options]
echo.
echo Commands:
echo   start     Start server
echo   stop      Stop server
echo   status    View server status
echo.
echo Options:
echo   --port PORT           Specify server port (default: %DEFAULT_SERVER_PORT%)
echo   --background          Run server in background
echo   --help                Show this help information
echo.
echo Examples:
echo   %~nx0 start                    # Start server in foreground
echo   %~nx0 start --background       # Start server in background
echo   %~nx0 start --port 8080        # Start server on port 8080
echo   %~nx0 stop                     # Stop server
echo   %~nx0 status                   # View server status
echo.
goto :eof

:init_logging
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
    call :log_info "Created log directory: %LOG_DIR%"
)
goto :eof

:setup_java_environment
REM Use system Java (already verified to be working)
java -version >nul 2>&1
if errorlevel 1 (
    call :log_error "Java runtime not installed or not in PATH"
    exit /b 1
)

REM Get Java version information
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set "java_version=%%i"
    goto :java_version_done
)
:java_version_done
call :log_info "Using Java version: %java_version%"
goto :eof

:check_jar_file
set "jar_path=%TARGET_DIR%\%JAR_NAME%"

if not exist "%jar_path%" (
    call :log_error "JAR file does not exist: %jar_path%"
    call :log_info "Please run 'mvn clean package' to compile the project first"
    exit /b 1
)

call :log_info "Found JAR file: %jar_path%"
goto :eof

:check_port
set "port=%~1"

REM Use netstat to check if port is occupied
netstat -an | findstr ":%port% " >nul 2>&1
if not errorlevel 1 (
    call :log_warning "Port %port% is already in use"
    exit /b 1
)
goto :eof

:get_server_pid
if exist "%PID_FILE%" (
    set /p server_pid=<"%PID_FILE%"
    REM Check if process is still running
    tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
    if not errorlevel 1 (
        exit /b 0
    ) else (
        REM PID file exists but process doesn't exist, delete PID file
        del "%PID_FILE%" >nul 2>&1
    )
)
exit /b 1

:start_server
set "port=%~1"
set "background=%~2"

call :log_info "Starting file transfer server..."

REM Check if server is already running
call :get_server_pid
if not errorlevel 1 (
    call :log_warning "Server is already running (PID: %server_pid%)"
    exit /b 1
)

REM Check port
call :check_port "%port%"
if errorlevel 1 (
    call :log_error "Cannot start server, port %port% is already in use"
    exit /b 1
)

REM Check JAR file
call :check_jar_file
if errorlevel 1 exit /b 1

REM Build Java command
set "jar_path=%TARGET_DIR%\%JAR_NAME%"
set "java_opts=-Xms512m -Xmx1g -XX:+UseG1GC"
set "spring_opts=--server.port=%port% --spring.profiles.active=%DEFAULT_PROFILE%"
set "java_cmd=java %java_opts% -jar %jar_path% %spring_opts%"

call :log_info "Java command: %java_cmd%"

if "%background%"=="true" (
    REM Background startup
    call :log_info "Starting server in background..."
    start /b "" %java_cmd% > "%LOG_FILE%" 2>&1
    
    REM Wait a moment for startup
    timeout /t 3 /nobreak >nul
    
    REM Simple health check
    call :log_info "Checking server health..."
    timeout /t 5 /nobreak >nul
    
    REM Check if port is now listening
    netstat -an | findstr ":%port% " >nul 2>&1
    if not errorlevel 1 (
        call :log_success "Server started successfully in background!"
        call :log_info "Server address: http://localhost:%port%"
        call :log_info "Health check: http://localhost:%port%/filetransfer/actuator/health"
        call :log_info "Log file: %LOG_FILE%"
    ) else (
        call :log_error "Server failed to start"
        exit /b 1
    )
) else (
    REM Foreground startup
    call :log_info "Starting server in foreground..."
    call :log_info "Press Ctrl+C to stop server"
    %java_cmd%
)
goto :eof

:stop_server
call :log_info "Stopping file transfer server..."

call :get_server_pid
if errorlevel 1 (
    call :log_warning "Server is not running"
    exit /b 0
)

call :log_info "Stopping server process (PID: %server_pid%)..."
taskkill /f /pid %server_pid% >nul 2>&1
del "%PID_FILE%" >nul 2>&1
call :log_success "Server stopped"
exit /b 0

:show_status
call :get_server_pid
if not errorlevel 1 (
    call :log_success "Server is running (PID: %server_pid%)"
    echo Server information:
    echo   PID: %server_pid%
    echo   Port: %DEFAULT_SERVER_PORT%
    echo   Server address: http://localhost:%DEFAULT_SERVER_PORT%
    echo   Health check: http://localhost:%DEFAULT_SERVER_PORT%/filetransfer/actuator/health
    echo   Log file: %LOG_FILE%
    echo   PID file: %PID_FILE%
) else (
    call :log_info "Server is not running"
)
goto :eof

REM ==================== Main Program ====================

:main
REM Show script header
call :show_header

REM Initialize logging
call :init_logging

REM Parse command line arguments (SIMPLIFIED)
set "command="
set "port=%DEFAULT_SERVER_PORT%"
set "background=false"

REM Simple argument parsing
if "%~1"=="start" set "command=start"
if "%~1"=="stop" set "command=stop"
if "%~1"=="status" set "command=status"
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)

REM Check for additional options
if "%~2"=="--background" set "background=true"
if "%~2"=="--port" set "port=%~3"
if "%~3"=="--background" set "background=true"

REM If no command specified, show help
if "%command%"=="" (
    call :show_help
    exit /b 1
)

REM Setup Java environment
call :setup_java_environment
if errorlevel 1 exit /b 1

REM Execute command
if "%command%"=="start" (
    call :start_server "%port%" "%background%"
) else if "%command%"=="stop" (
    call :stop_server
) else if "%command%"=="status" (
    call :show_status
) else (
    call :log_error "Unknown command: %command%"
    exit /b 1
)

goto :eof

REM Execute main function
call :main %*
